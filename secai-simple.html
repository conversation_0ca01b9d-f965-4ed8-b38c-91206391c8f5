<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecAI 多智能体网络安全专家系统</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .content {
            padding: 40px;
        }
        
        .diagram-section {
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
        }
        
        .diagram-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .mermaid {
            text-align: center;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ SecAI 多智能体网络安全专家系统</h1>
            <p>基于 Easy LLM CLI 的网络安全专家智能体工作流程</p>
        </div>
        
        <div class="content">
            <!-- 系统架构图 -->
            <div class="diagram-section">
                <h2 class="diagram-title">🏗️ 系统整体架构</h2>
                <div class="mermaid">
flowchart TD
    A[用户输入] --> B[SecAI CLI]
    B --> C[主控制器]
    C --> D[任务分析器]
    D --> E[智能体路由器]
    
    E --> F1[威胁分析专家]
    E --> F2[漏洞评估专家]
    E --> F3[事件响应专家]
    E --> F4[渗透测试专家]
    E --> F5[合规审计专家]
    E --> F6[威胁情报专家]
    
    F1 --> G[Agent Loop]
    F2 --> G
    F3 --> G
    F4 --> G
    F5 --> G
    F6 --> G
    
    G --> H1[思考阶段]
    G --> H2[行动阶段]
    G --> H3[观察阶段]
    
    H1 --> H2
    H2 --> H3
    H3 --> H1
    
    H2 --> I[工具执行系统]
    I --> J1[通用工具]
    I --> J2[安全工具]
    I --> J3[MCP工具]
    
    H1 --> K[安全知识库]
    H1 --> L[安全思维链]
    
    I --> M[输出系统]
    M --> B
    
    style A fill:#e3f2fd
    style G fill:#fff3e0
    style I fill:#f3e5f5
    style K fill:#e8f5e8
    style L fill:#ffebee
                </div>
            </div>
            
            <!-- Agent Loop 详细流程 -->
            <div class="diagram-section">
                <h2 class="diagram-title">🔄 Agent Loop 详细流程</h2>
                <div class="mermaid">
flowchart TD
    A[用户查询] --> B[输入处理]
    B --> C[任务分类]
    C --> D[选择智能体]
    D --> E[智能体激活]
    
    E --> F[思考阶段]
    F --> G[分析任务]
    F --> H[访问知识库]
    F --> I[制定策略]
    F --> J[选择工具]
    
    J --> K[行动阶段]
    K --> L[验证工具]
    K --> M[执行工具]
    K --> N[监控执行]
    K --> O[处理错误]
    
    O --> P[观察阶段]
    P --> Q[收集结果]
    P --> R[分析数据]
    P --> S[更新知识]
    P --> T[评估进度]
    
    T --> U{任务完成?}
    U -->|否| F
    U -->|是| V[生成输出]
    
    V --> W[格式化结果]
    W --> X[安全报告]
    W --> Y[行动建议]
    W --> Z[用户界面]
    
    style F fill:#e3f2fd
    style K fill:#f3e5f5
    style P fill:#e8f5e8
    style V fill:#fff3e0
                </div>
            </div>
            
            <!-- 工具执行流程 -->
            <div class="diagram-section">
                <h2 class="diagram-title">⚙️ 工具执行流程</h2>
                <div class="mermaid">
flowchart LR
    A[工具调用] --> B[注册表查找]
    B --> C[参数验证]
    C --> D[安全检查]
    D --> E[工具调度]
    E --> F{并行执行?}
    F -->|是| G[并行工具]
    F -->|否| H[顺序工具]
    G --> I[结果聚合]
    H --> I
    I --> J[工具响应]
    
    style A fill:#e3f2fd
    style D fill:#ffebee
    style I fill:#e8f5e8
                </div>
            </div>
        </div>
    </div>
    
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
