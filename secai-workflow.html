<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecAI 多智能体网络安全专家系统工作流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .diagram-section {
            margin-bottom: 50px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .diagram-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .diagram-description {
            color: #666;
            margin-bottom: 30px;
            text-align: center;
            font-size: 1.1em;
            line-height: 1.6;
        }
        
        .mermaid {
            text-align: center;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        
        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #3498db;
        }
        
        .feature-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 0;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ SecAI 多智能体网络安全专家系统</h1>
            <p>基于 Easy LLM CLI 的网络安全专家智能体工作流程</p>
        </div>
        
        <div class="content">
            <!-- 主架构图 -->
            <div class="diagram-section">
                <h2 class="diagram-title">🏗️ 系统整体架构</h2>
                <p class="diagram-description">
                    展示 SecAI 系统的完整架构，包括多智能体协作、工具系统、安全控制和知识库等核心组件
                </p>
                <div class="mermaid">
graph TB
    %% 用户交互层
    User[👤 用户输入<br/>安全查询/任务] --> CLI[🖥️ SecAI CLI<br/>命令行界面]
    
    %% 主控制器
    CLI --> MainController[🎯 SecAI 主控制器<br/>Agent Orchestrator]
    
    %% 任务分析和路由
    MainController --> TaskAnalyzer[🔍 任务分析器<br/>Task Analyzer]
    TaskAnalyzer --> Router[🚦 智能体路由器<br/>Agent Router]
    
    %% 多智能体专家系统
    Router --> ThreatAgent[🛡️ 威胁分析专家<br/>Threat Analysis Agent]
    Router --> VulnAgent[🔍 漏洞评估专家<br/>Vulnerability Assessment Agent]
    Router --> IRAgent[🚨 事件响应专家<br/>Incident Response Agent]
    Router --> PentestAgent[⚔️ 渗透测试专家<br/>Penetration Testing Agent]
    Router --> ComplianceAgent[📋 合规审计专家<br/>Compliance Audit Agent]
    Router --> ThreatIntelAgent[🕵️ 威胁情报专家<br/>Threat Intelligence Agent]
    
    %% Agent Loop 核心循环
    subgraph AgentLoop["🔄 Agent Loop 核心循环"]
        direction TB
        Think[💭 思考阶段<br/>Reasoning & Planning]
        Act[⚡ 行动阶段<br/>Tool Execution]
        Observe[👁️ 观察阶段<br/>Result Analysis]
        
        Think --> Act
        Act --> Observe
        Observe --> Think
    end
    
    %% 工具系统
    subgraph ToolSystem["🛠️ 安全工具系统"]
        direction TB
        
        %% 保留的通用工具
        subgraph GeneralTools["通用工具"]
            ReadTool[📖 文件读取工具]
            EditTool[✏️ 代码编辑工具]
            ShellTool[💻 Shell执行工具]
            GitTool[📚 Git版本控制]
            MemoryTool[🧠 记忆管理工具]
        end
        
        %% 安全专用工具
        subgraph SecurityTools["安全专用工具"]
            NetworkScan[🌐 网络扫描工具]
            VulnScan[🔍 漏洞扫描工具]
            ThreatIntel[🕵️ 威胁情报工具]
            LogAnalysis[📊 日志分析工具]
            Forensics[🔬 取证分析工具]
            Compliance[📋 合规检查工具]
            CodeSecurity[🔒 代码安全工具]
            ReverseEng[🔄 逆向工程工具]
            MalwareAnalysis[🦠 恶意软件分析]
        end
        
        %% MCP工具
        subgraph MCPTools["MCP 扩展工具"]
            MCPServer1[🔌 MCP Server 1]
            MCPServer2[🔌 MCP Server 2]
            MCPServerN[🔌 MCP Server N]
        end
    end
    
    %% 工具调度和执行
    subgraph ToolExecution["⚙️ 工具执行系统"]
        direction TB
        ToolRegistry[📚 工具注册表<br/>Tool Registry]
        ToolScheduler[📅 工具调度器<br/>Core Tool Scheduler]
        ToolValidator[✅ 工具验证器<br/>Tool Validator]
        ToolExecutor[⚡ 工具执行器<br/>Tool Executor]
        
        ToolRegistry --> ToolScheduler
        ToolScheduler --> ToolValidator
        ToolValidator --> ToolExecutor
    end
    
    %% 安全知识库
    subgraph KnowledgeBase["📚 安全知识库"]
        direction TB
        CVEDatabase[🗃️ CVE 漏洞数据库]
        ThreatIntelDB[🕵️ 威胁情报数据库]
        ComplianceFrameworks[📋 合规框架库]
        SecurityPatterns[🔒 安全模式库]
        UserMemory[🧠 用户记忆库]
    end
    
    %% 思维链系统
    subgraph ThinkingChains["🧠 安全思维链"]
        direction TB
        ThreatChain[🛡️ 威胁分析链<br/>资产识别→威胁建模→攻击面分析→风险评估→缓解措施]
        IRChain[🚨 事件响应链<br/>检测确认→影响评估→遏制措施→根因分析→恢复计划]
        PentestChain[⚔️ 渗透测试链<br/>信息收集→漏洞发现→漏洞利用→权限提升→横向移动]
        ForensicsChain[🔬 取证分析链<br/>证据保全→数据提取→时间线重建→关联分析→报告生成]
    end
    
    %% 连接关系
    ThreatAgent --> AgentLoop
    VulnAgent --> AgentLoop
    IRAgent --> AgentLoop
    PentestAgent --> AgentLoop
    ComplianceAgent --> AgentLoop
    ThreatIntelAgent --> AgentLoop
    
    AgentLoop --> ToolExecution
    ToolExecution --> ToolSystem
    
    %% 智能体访问知识库
    ThreatAgent -.-> KnowledgeBase
    VulnAgent -.-> KnowledgeBase
    IRAgent -.-> KnowledgeBase
    PentestAgent -.-> KnowledgeBase
    ComplianceAgent -.-> KnowledgeBase
    ThreatIntelAgent -.-> KnowledgeBase
    
    %% 智能体使用思维链
    ThreatAgent -.-> ThinkingChains
    VulnAgent -.-> ThinkingChains
    IRAgent -.-> ThinkingChains
    PentestAgent -.-> ThinkingChains
    ComplianceAgent -.-> ThinkingChains
    ThreatIntelAgent -.-> ThinkingChains
    
    %% 结果处理和输出
    subgraph OutputSystem["📤 输出系统"]
        direction TB
        ResultProcessor[📊 结果处理器]
        ReportGenerator[📄 报告生成器]
        Visualizer[📈 可视化工具]
    end
    
    ToolExecutor --> OutputSystem
    OutputSystem --> CLI
    
    %% 安全控制
    subgraph SecurityControls["🔐 安全控制"]
        direction TB
        AccessControl[🔑 访问控制]
        AuditLog[📝 审计日志]
        Sandbox[🏖️ 沙箱环境]
        WhiteList[📋 命令白名单]
    end
    
    ToolExecution -.-> SecurityControls
    
    %% 样式定义
    classDef agentClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef toolClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef systemClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef securityClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class ThreatAgent,VulnAgent,IRAgent,PentestAgent,ComplianceAgent,ThreatIntelAgent agentClass
    class ToolSystem,GeneralTools,SecurityTools,MCPTools toolClass
    class MainController,TaskAnalyzer,Router,AgentLoop,ToolExecution systemClass
    class SecurityControls,KnowledgeBase,ThinkingChains securityClass
                </div>
            </div>
            
            <!-- Agent Loop 详细图 -->
            <div class="diagram-section">
                <h2 class="diagram-title">🔄 Agent Loop 详细工作流程</h2>
                <p class="diagram-description">
                    展示 Think-Act-Observe 循环的详细实现，包括工具执行、安全思维链和知识库交互
                </p>
                <div class="mermaid">
graph TB
    %% 用户输入
    UserInput[👤 用户安全查询] --> InputProcessor[📝 输入处理器]
    
    %% 任务分析和智能体选择
    InputProcessor --> TaskClassifier[🔍 任务分类器<br/>识别安全领域]
    TaskClassifier --> AgentSelector[🎯 智能体选择器<br/>选择最适合的专家]
    
    %% 智能体激活
    AgentSelector --> SelectedAgent[🤖 选中的安全专家智能体]
    
    %% Agent Loop 详细流程
    subgraph AgentLoop["🔄 Agent Loop 核心循环"]
        direction TB
        
        %% 思考阶段
        subgraph ThinkPhase["💭 思考阶段 (Think)"]
            direction TB
            AnalyzeTask[🔍 分析任务需求]
            AccessMemory[🧠 访问安全知识库]
            PlanStrategy[📋 制定执行策略]
            SelectTools[🛠️ 选择所需工具]
            
            AnalyzeTask --> AccessMemory
            AccessMemory --> PlanStrategy
            PlanStrategy --> SelectTools
        end
        
        %% 行动阶段
        subgraph ActPhase["⚡ 行动阶段 (Act)"]
            direction TB
            ValidateTools[✅ 验证工具可用性]
            ExecuteTools[🔧 执行安全工具]
            MonitorExecution[👁️ 监控执行过程]
            HandleErrors[❌ 处理执行错误]
            
            ValidateTools --> ExecuteTools
            ExecuteTools --> MonitorExecution
            MonitorExecution --> HandleErrors
        end
        
        %% 观察阶段
        subgraph ObservePhase["👁️ 观察阶段 (Observe)"]
            direction TB
            CollectResults[📊 收集执行结果]
            AnalyzeResults[🔬 分析结果数据]
            UpdateMemory[💾 更新知识库]
            EvaluateProgress[📈 评估任务进度]
            
            CollectResults --> AnalyzeResults
            AnalyzeResults --> UpdateMemory
            UpdateMemory --> EvaluateProgress
        end
        
        %% 循环控制
        ThinkPhase --> ActPhase
        ActPhase --> ObservePhase
        ObservePhase --> Decision{🤔 任务是否完成?}
        Decision -->|否| ThinkPhase
        Decision -->|是| FinalOutput[📤 生成最终输出]
    end
    
    %% 工具执行详细流程
    subgraph ToolExecutionDetail["⚙️ 工具执行详细流程"]
        direction TB
        
        ToolRequest[📞 工具调用请求] --> ToolRegistry[📚 工具注册表查找]
        ToolRegistry --> ToolValidation[✅ 工具参数验证]
        ToolValidation --> SecurityCheck[🔐 安全权限检查]
        SecurityCheck --> ToolScheduler[📅 工具调度器]
        
        ToolScheduler --> ParallelExecution{🔀 是否并行执行?}
        ParallelExecution -->|是| ParallelTools[⚡ 并行工具执行]
        ParallelExecution -->|否| SequentialTools[📝 顺序工具执行]
        
        ParallelTools --> ResultAggregator[📊 结果聚合器]
        SequentialTools --> ResultAggregator
        
        ResultAggregator --> ToolResponse[📋 工具响应]
    end
    
    %% 连接关系
    SelectedAgent --> AgentLoop
    ActPhase --> ToolExecutionDetail
    
    %% 最终输出处理
    FinalOutput --> OutputFormatter[📄 输出格式化器]
    OutputFormatter --> SecurityReport[📊 安全分析报告]
    OutputFormatter --> Recommendations[💡 安全建议]
    OutputFormatter --> ActionItems[✅ 行动项清单]
    
    SecurityReport --> UserInterface[🖥️ 用户界面]
    Recommendations --> UserInterface
    ActionItems --> UserInterface
    
    %% 样式定义
    classDef thinkClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef actClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef observeClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef toolClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef securityClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class ThinkPhase,AnalyzeTask,AccessMemory,PlanStrategy,SelectTools thinkClass
    class ActPhase,ValidateTools,ExecuteTools,MonitorExecution,HandleErrors actClass
    class ObservePhase,CollectResults,AnalyzeResults,UpdateMemory,EvaluateProgress observeClass
    class ToolExecutionDetail,ToolRequest,ToolRegistry,ToolValidation,ToolScheduler toolClass
    class SecurityCheck,UserInterface securityClass
                </div>
            </div>
            
            <!-- 核心特性 -->
            <div class="features">
                <div class="feature-card">
                    <h3>🤖 多智能体协作</h3>
                    <p>6个专业安全智能体协同工作，覆盖威胁分析、漏洞评估、事件响应、渗透测试、合规审计和威胁情报等核心安全领域。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🔄 Agent Loop 循环</h3>
                    <p>Think-Act-Observe 循环确保智能体能够持续学习、优化决策，并根据执行结果调整策略。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🛠️ 丰富工具链</h3>
                    <p>集成网络扫描、漏洞检测、威胁情报、日志分析、取证分析等专业安全工具，支持MCP协议扩展。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🧠 安全思维链</h3>
                    <p>内置威胁分析、事件响应、渗透测试等专业思维链，确保分析过程符合安全最佳实践。</p>
                </div>
                
                <div class="feature-card">
                    <h3>📚 知识库管理</h3>
                    <p>集成CVE数据库、威胁情报、合规框架等安全知识，支持动态更新和智能检索。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🔐 安全控制</h3>
                    <p>多层安全控制机制，包括权限管理、审计日志、沙箱环境和命令白名单等安全保障。</p>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 SecAI - 基于 Easy LLM CLI 的网络安全多智能体专家系统</p>
        </div>
    </div>
    
    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            themeVariables: {
                primaryColor: '#3498db',
                primaryTextColor: '#2c3e50',
                primaryBorderColor: '#2980b9',
                lineColor: '#34495e',
                secondaryColor: '#ecf0f1',
                tertiaryColor: '#f8f9fa'
            }
        });
        
        // 添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为图表添加缩放功能
            const diagrams = document.querySelectorAll('.mermaid');
            diagrams.forEach(diagram => {
                diagram.style.cursor = 'zoom-in';
                diagram.addEventListener('click', function() {
                    if (this.style.transform === 'scale(1.2)') {
                        this.style.transform = 'scale(1)';
                        this.style.cursor = 'zoom-in';
                    } else {
                        this.style.transform = 'scale(1.2)';
                        this.style.cursor = 'zoom-out';
                    }
                    this.style.transition = 'transform 0.3s ease';
                });
            });
        });
    </script>
</body>
</html>
