<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecAI 多智能体网络安全专家系统工作流程</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .diagram-section {
            margin-bottom: 50px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .diagram-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }
        
        .diagram-description {
            color: #666;
            margin-bottom: 30px;
            text-align: center;
            font-size: 1.1em;
            line-height: 1.6;
        }
        
        .mermaid {
            text-align: center;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        
        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #3498db;
        }
        
        .feature-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 0;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ SecAI 多智能体网络安全专家系统</h1>
            <p>基于 Easy LLM CLI 的网络安全专家智能体工作流程</p>
        </div>
        
        <div class="content">
            <!-- 主架构图 -->
            <div class="diagram-section">
                <h2 class="diagram-title">🏗️ 系统整体架构</h2>
                <p class="diagram-description">
                    展示 SecAI 系统的完整架构，包括多智能体协作、工具系统、安全控制和知识库等核心组件
                </p>
                <div class="mermaid">
graph TB
    User[用户输入] --> CLI[SecAI CLI]
    CLI --> MainController[SecAI 主控制器]
    MainController --> TaskAnalyzer[任务分析器]
    TaskAnalyzer --> Router[智能体路由器]

    Router --> ThreatAgent[威胁分析专家]
    Router --> VulnAgent[漏洞评估专家]
    Router --> IRAgent[事件响应专家]
    Router --> PentestAgent[渗透测试专家]
    Router --> ComplianceAgent[合规审计专家]
    Router --> ThreatIntelAgent[威胁情报专家]

    ThreatAgent --> AgentLoop[Agent Loop 循环]
    VulnAgent --> AgentLoop
    IRAgent --> AgentLoop
    PentestAgent --> AgentLoop
    ComplianceAgent --> AgentLoop
    ThreatIntelAgent --> AgentLoop

    AgentLoop --> Think[思考阶段]
    AgentLoop --> Act[行动阶段]
    AgentLoop --> Observe[观察阶段]

    Think --> Act
    Act --> Observe
    Observe --> Think

    Act --> ToolExecution[工具执行系统]
    ToolExecution --> ToolRegistry[工具注册表]
    ToolExecution --> ToolScheduler[工具调度器]
    ToolExecution --> ToolValidator[工具验证器]
    ToolExecution --> ToolExecutor[工具执行器]

    ToolExecutor --> GeneralTools[通用工具]
    ToolExecutor --> SecurityTools[安全专用工具]
    ToolExecutor --> MCPTools[MCP 扩展工具]

    GeneralTools --> ReadTool[文件读取工具]
    GeneralTools --> EditTool[代码编辑工具]
    GeneralTools --> ShellTool[Shell执行工具]
    GeneralTools --> GitTool[Git版本控制]
    GeneralTools --> MemoryTool[记忆管理工具]

    SecurityTools --> NetworkScan[网络扫描工具]
    SecurityTools --> VulnScan[漏洞扫描工具]
    SecurityTools --> ThreatIntel[威胁情报工具]
    SecurityTools --> LogAnalysis[日志分析工具]
    SecurityTools --> Forensics[取证分析工具]
    SecurityTools --> Compliance[合规检查工具]

    Think --> KnowledgeBase[安全知识库]
    KnowledgeBase --> CVEDatabase[CVE 漏洞数据库]
    KnowledgeBase --> ThreatIntelDB[威胁情报数据库]
    KnowledgeBase --> ComplianceFrameworks[合规框架库]
    KnowledgeBase --> SecurityPatterns[安全模式库]

    Think --> ThinkingChains[安全思维链]
    ThinkingChains --> ThreatChain[威胁分析链]
    ThinkingChains --> IRChain[事件响应链]
    ThinkingChains --> PentestChain[渗透测试链]
    ThinkingChains --> ForensicsChain[取证分析链]

    ToolExecutor --> OutputSystem[输出系统]
    OutputSystem --> ResultProcessor[结果处理器]
    OutputSystem --> ReportGenerator[报告生成器]
    OutputSystem --> Visualizer[可视化工具]

    OutputSystem --> CLI

    ToolExecution --> SecurityControls[安全控制]
    SecurityControls --> AccessControl[访问控制]
    SecurityControls --> AuditLog[审计日志]
    SecurityControls --> Sandbox[沙箱环境]
    SecurityControls --> WhiteList[命令白名单]

    classDef agentClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef toolClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef systemClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef securityClass fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class ThreatAgent,VulnAgent,IRAgent,PentestAgent,ComplianceAgent,ThreatIntelAgent agentClass
    class GeneralTools,SecurityTools,MCPTools,ReadTool,EditTool,ShellTool,GitTool,MemoryTool,NetworkScan,VulnScan,ThreatIntel,LogAnalysis,Forensics,Compliance toolClass
    class MainController,TaskAnalyzer,Router,AgentLoop,ToolExecution,Think,Act,Observe systemClass
    class SecurityControls,KnowledgeBase,ThinkingChains,AccessControl,AuditLog,Sandbox,WhiteList securityClass
                </div>
            </div>
            
            <!-- Agent Loop 详细图 -->
            <div class="diagram-section">
                <h2 class="diagram-title">🔄 Agent Loop 详细工作流程</h2>
                <p class="diagram-description">
                    展示 Think-Act-Observe 循环的详细实现，包括工具执行、安全思维链和知识库交互
                </p>
                <div class="mermaid">
graph TB
    UserInput[用户安全查询] --> InputProcessor[输入处理器]
    InputProcessor --> TaskClassifier[任务分类器]
    TaskClassifier --> AgentSelector[智能体选择器]
    AgentSelector --> SelectedAgent[选中的安全专家智能体]

    SelectedAgent --> Think[思考阶段 Think]
    Think --> Act[行动阶段 Act]
    Act --> Observe[观察阶段 Observe]
    Observe --> Decision{任务完成?}
    Decision -->|否| Think
    Decision -->|是| FinalOutput[生成最终输出]

    Think --> AnalyzeTask[分析任务需求]
    Think --> AccessMemory[访问安全知识库]
    Think --> PlanStrategy[制定执行策略]
    Think --> SelectTools[选择所需工具]

    Act --> ValidateTools[验证工具可用性]
    Act --> ExecuteTools[执行安全工具]
    Act --> MonitorExecution[监控执行过程]
    Act --> HandleErrors[处理执行错误]

    Observe --> CollectResults[收集执行结果]
    Observe --> AnalyzeResults[分析结果数据]
    Observe --> UpdateMemory[更新知识库]
    Observe --> EvaluateProgress[评估任务进度]

    ExecuteTools --> ToolRequest[工具调用请求]
    ToolRequest --> ToolRegistry[工具注册表查找]
    ToolRegistry --> ToolValidation[工具参数验证]
    ToolValidation --> SecurityCheck[安全权限检查]
    SecurityCheck --> ToolScheduler[工具调度器]
    ToolScheduler --> ToolResponse[工具响应]

    FinalOutput --> OutputFormatter[输出格式化器]
    OutputFormatter --> SecurityReport[安全分析报告]
    OutputFormatter --> Recommendations[安全建议]
    OutputFormatter --> ActionItems[行动项清单]

    classDef thinkClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef actClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef observeClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef toolClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class Think,AnalyzeTask,AccessMemory,PlanStrategy,SelectTools thinkClass
    class Act,ValidateTools,ExecuteTools,MonitorExecution,HandleErrors actClass
    class Observe,CollectResults,AnalyzeResults,UpdateMemory,EvaluateProgress observeClass
    class ToolRequest,ToolRegistry,ToolValidation,ToolScheduler,ToolResponse toolClass
                </div>
            </div>
            
            <!-- 核心特性 -->
            <div class="features">
                <div class="feature-card">
                    <h3>🤖 多智能体协作</h3>
                    <p>6个专业安全智能体协同工作，覆盖威胁分析、漏洞评估、事件响应、渗透测试、合规审计和威胁情报等核心安全领域。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🔄 Agent Loop 循环</h3>
                    <p>Think-Act-Observe 循环确保智能体能够持续学习、优化决策，并根据执行结果调整策略。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🛠️ 丰富工具链</h3>
                    <p>集成网络扫描、漏洞检测、威胁情报、日志分析、取证分析等专业安全工具，支持MCP协议扩展。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🧠 安全思维链</h3>
                    <p>内置威胁分析、事件响应、渗透测试等专业思维链，确保分析过程符合安全最佳实践。</p>
                </div>
                
                <div class="feature-card">
                    <h3>📚 知识库管理</h3>
                    <p>集成CVE数据库、威胁情报、合规框架等安全知识，支持动态更新和智能检索。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🔐 安全控制</h3>
                    <p>多层安全控制机制，包括权限管理、审计日志、沙箱环境和命令白名单等安全保障。</p>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 SecAI - 基于 Easy LLM CLI 的网络安全多智能体专家系统</p>
        </div>
    </div>
    
    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            themeVariables: {
                primaryColor: '#3498db',
                primaryTextColor: '#2c3e50',
                primaryBorderColor: '#2980b9',
                lineColor: '#34495e',
                secondaryColor: '#ecf0f1',
                tertiaryColor: '#f8f9fa'
            }
        });
        
        // 添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为图表添加缩放功能
            const diagrams = document.querySelectorAll('.mermaid');
            diagrams.forEach(diagram => {
                diagram.style.cursor = 'zoom-in';
                diagram.addEventListener('click', function() {
                    if (this.style.transform === 'scale(1.2)') {
                        this.style.transform = 'scale(1)';
                        this.style.cursor = 'zoom-in';
                    } else {
                        this.style.transform = 'scale(1.2)';
                        this.style.cursor = 'zoom-out';
                    }
                    this.style.transition = 'transform 0.3s ease';
                });
            });
        });
    </script>
</body>
</html>
