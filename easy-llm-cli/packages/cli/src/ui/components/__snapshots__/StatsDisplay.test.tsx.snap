// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<StatsDisplay /> > Conditional Color Tests > renders success rate in green for high values 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Session Stats                                                                                   │
│                                                                                                  │
│  Interaction Summary                                                                             │
│  Tool Calls:                 10 ( ✔ 10 ✖ 0 )                                                     │
│  Success Rate:               100.0%                                                              │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1s                                                                  │
│  Agent Active:               0s                                                                  │
│    » API Time:               0s (0.0%)                                                           │
│    » Tool Time:              0s (0.0%)                                                           │
│                                                                                                  │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<StatsDisplay /> > Conditional Color Tests > renders success rate in red for low values 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Session Stats                                                                                   │
│                                                                                                  │
│  Interaction Summary                                                                             │
│  Tool Calls:                 10 ( ✔ 5 ✖ 5 )                                                      │
│  Success Rate:               50.0%                                                               │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1s                                                                  │
│  Agent Active:               0s                                                                  │
│    » API Time:               0s (0.0%)                                                           │
│    » Tool Time:              0s (0.0%)                                                           │
│                                                                                                  │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<StatsDisplay /> > Conditional Color Tests > renders success rate in yellow for medium values 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Session Stats                                                                                   │
│                                                                                                  │
│  Interaction Summary                                                                             │
│  Tool Calls:                 10 ( ✔ 9 ✖ 1 )                                                      │
│  Success Rate:               90.0%                                                               │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1s                                                                  │
│  Agent Active:               0s                                                                  │
│    » API Time:               0s (0.0%)                                                           │
│    » Tool Time:              0s (0.0%)                                                           │
│                                                                                                  │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<StatsDisplay /> > Conditional Rendering Tests > hides Efficiency section when cache is not used 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Session Stats                                                                                   │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1s                                                                  │
│  Agent Active:               100ms                                                               │
│    » API Time:               100ms (100.0%)                                                      │
│    » Tool Time:              0s (0.0%)                                                           │
│                                                                                                  │
│                                                                                                  │
│  Model Usage                  Reqs   Input Tokens  Output Tokens                                 │
│  ───────────────────────────────────────────────────────────────                                 │
│  gemini-2.5-pro                  1            100            100                                 │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<StatsDisplay /> > Conditional Rendering Tests > hides User Agreement when no decisions are made 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Session Stats                                                                                   │
│                                                                                                  │
│  Interaction Summary                                                                             │
│  Tool Calls:                 2 ( ✔ 1 ✖ 1 )                                                       │
│  Success Rate:               50.0%                                                               │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1s                                                                  │
│  Agent Active:               123ms                                                               │
│    » API Time:               0s (0.0%)                                                           │
│    » Tool Time:              123ms (100.0%)                                                      │
│                                                                                                  │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<StatsDisplay /> > Title Rendering > renders the custom title when a title prop is provided 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Agent powering down. Goodbye!                                                                   │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1s                                                                  │
│  Agent Active:               0s                                                                  │
│    » API Time:               0s (0.0%)                                                           │
│    » Tool Time:              0s (0.0%)                                                           │
│                                                                                                  │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<StatsDisplay /> > Title Rendering > renders the default title when no title prop is provided 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Session Stats                                                                                   │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1s                                                                  │
│  Agent Active:               0s                                                                  │
│    » API Time:               0s (0.0%)                                                           │
│    » Tool Time:              0s (0.0%)                                                           │
│                                                                                                  │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<StatsDisplay /> > renders a table with two models correctly 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Session Stats                                                                                   │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1s                                                                  │
│  Agent Active:               19.5s                                                               │
│    » API Time:               19.5s (100.0%)                                                      │
│    » Tool Time:              0s (0.0%)                                                           │
│                                                                                                  │
│                                                                                                  │
│  Model Usage                  Reqs   Input Tokens  Output Tokens                                 │
│  ───────────────────────────────────────────────────────────────                                 │
│  gemini-2.5-pro                  3          1,000          2,000                                 │
│  gemini-2.5-flash                5         25,000         15,000                                 │
│                                                                                                  │
│  Savings Highlight: 10,500 (40.4%) of input tokens were served from the cache, reducing costs.   │
│                                                                                                  │
│  » Tip: For a full token breakdown, run \`/stats model\`.                                          │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<StatsDisplay /> > renders all sections when all data is present 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Session Stats                                                                                   │
│                                                                                                  │
│  Interaction Summary                                                                             │
│  Tool Calls:                 2 ( ✔ 1 ✖ 1 )                                                       │
│  Success Rate:               50.0%                                                               │
│  User Agreement:             100.0% (1 reviewed)                                                 │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1s                                                                  │
│  Agent Active:               223ms                                                               │
│    » API Time:               100ms (44.8%)                                                       │
│    » Tool Time:              123ms (55.2%)                                                       │
│                                                                                                  │
│                                                                                                  │
│  Model Usage                  Reqs   Input Tokens  Output Tokens                                 │
│  ───────────────────────────────────────────────────────────────                                 │
│  gemini-2.5-pro                  1            100            100                                 │
│                                                                                                  │
│  Savings Highlight: 50 (50.0%) of input tokens were served from the cache, reducing costs.       │
│                                                                                                  │
│  » Tip: For a full token breakdown, run \`/stats model\`.                                          │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;

exports[`<StatsDisplay /> > renders only the Performance section in its zero state 1`] = `
"╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
│                                                                                                  │
│  Session Stats                                                                                   │
│                                                                                                  │
│  Performance                                                                                     │
│  Wall Time:                  1s                                                                  │
│  Agent Active:               0s                                                                  │
│    » API Time:               0s (0.0%)                                                           │
│    » Tool Time:              0s (0.0%)                                                           │
│                                                                                                  │
│                                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯"
`;
